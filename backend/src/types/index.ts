// Backend types for MiCA Therapy Simulation

export interface AgentConfig {
  model: string;
  temperature: number;
  maxTokens: number;
  systemPrompt: string;
  persona?: string;
}

// Therapist Persona Strategy Types
export type TherapistPersonaType = 'cbt-only' | 'mi-fixed-pretreatment' | 'dynamic-adaptive';

// New therapeutic mode types for mode-specific analysis
export type TherapeuticMode = 'CBT' | 'DBT' | 'psychodynamic';

export interface TherapeuticModeConfig {
  mode: TherapeuticMode;
  name: string;
  description: string;
  analysisFramework: string;
  primaryFocus: string[];
  techniques: string[];
}

export interface TherapistPersonaConfig {
  id: TherapistPersonaType;
  name: string;
  description: string;
  strategy: TherapeuticStrategy;
}

export interface TherapeuticStrategy {
  type: TherapistPersonaType;
  readinessThreshold?: number; // For switching strategies
  allowSwitching: boolean;
  initialApproach: 'CBT' | 'MI';
  switchingRules?: {
    cbtThreshold: number;
    miThreshold: number;
    allowReverseSwitch: boolean;
  };
}

// Therapeutic Framework Types
export interface TherapeuticApproachInfo {
  id: 'motivational-interviewing' | 'cognitive-behavioral-therapy';
  name: string;
  selectedTechnique: {
    id: string;
    name: string;
    description: string;
  };
}

export interface ReadinessScore {
  score: number; // 1-10 scale
  factors: {
    sentiment: {
      value: 'positive' | 'negative' | 'neutral';
      weight: number;
      contribution: number;
    };
    sentimentIntensity: {
      value: 'low' | 'medium' | 'high';
      weight: number;
      contribution: number;
    };
    motivation: {
      value: 'low' | 'medium' | 'high';
      weight: number;
      contribution: number;
    };
    motivationType: {
      value: 'intrinsic' | 'extrinsic' | 'mixed';
      weight: number;
      contribution: number;
    };
    engagement: {
      value: 'low' | 'medium' | 'high';
      weight: number;
      contribution: number;
    };
    engagementPatterns: {
      value: string[];
      weight: number;
      contribution: number;
    };
  };
  reasoning: string;
  recommendedApproach: 'CBT' | 'MI';
  indicators: {
    positive: string[];
    negative: string[];
  };
}

export interface PatientAnalysis {
  sentiment: 'positive' | 'negative' | 'neutral';
  sentimentIntensity: 'low' | 'medium' | 'high';
  motivationLevel: 'low' | 'medium' | 'high';
  motivationType: 'intrinsic' | 'extrinsic' | 'mixed';
  engagementLevel: 'low' | 'medium' | 'high';
  engagementPatterns: string[];
  readinessScore: ReadinessScore;
}

// Mode-specific client state analysis interfaces
export interface CBTClientStateAnalysis {
  cognitivePatterns: {
    thoughtDistortions: string[];
    automaticThoughts: string[];
    cognitiveFlexibility: 'low' | 'medium' | 'high';
    insightLevel: 'low' | 'medium' | 'high';
  };
  behavioralTriggers: {
    identifiedTriggers: string[];
    avoidanceBehaviors: string[];
    copingStrategies: string[];
    behavioralActivation: 'low' | 'medium' | 'high';
  };
  moodTracking: {
    currentMood: 'depressed' | 'anxious' | 'neutral' | 'elevated' | 'mixed';
    moodStability: 'stable' | 'fluctuating' | 'volatile';
    moodTriggers: string[];
  };
  readinessForCBT: {
    homeworkCompliance: 'low' | 'medium' | 'high';
    structuredApproachAcceptance: 'low' | 'medium' | 'high';
    goalOrientation: 'low' | 'medium' | 'high';
  };
}

export interface DBTClientStateAnalysis {
  emotionalRegulation: {
    emotionalIntensity: 'low' | 'medium' | 'high' | 'extreme';
    emotionalLability: 'stable' | 'moderate' | 'high';
    regulationSkills: string[];
    dysregulationTriggers: string[];
  };
  distressTolerance: {
    currentDistressLevel: 'low' | 'medium' | 'high' | 'crisis';
    toleranceCapacity: 'low' | 'medium' | 'high';
    crisisSkillsUsage: string[];
    selfHarmRisk: 'none' | 'low' | 'medium' | 'high';
  };
  interpersonalEffectiveness: {
    relationshipPatterns: string[];
    boundaryIssues: string[];
    communicationStyle: 'passive' | 'aggressive' | 'assertive' | 'passive-aggressive';
    interpersonalGoals: string[];
  };
  mindfulnessStates: {
    presentMomentAwareness: 'low' | 'medium' | 'high';
    mindfulnessSkills: string[];
    dissociationLevel: 'none' | 'mild' | 'moderate' | 'severe';
    wiseMindAccess: 'low' | 'medium' | 'high';
  };
}

export interface PsychodynamicClientStateAnalysis {
  unconsciousPatterns: {
    repeatingThemes: string[];
    conflictualPatterns: string[];
    unconsciousMotivations: string[];
    symbolism: string[];
  };
  defenseMechanisms: {
    primaryDefenses: string[];
    adaptiveDefenses: string[];
    maladaptiveDefenses: string[];
    defenseFlexibility: 'rigid' | 'moderate' | 'flexible';
  };
  transferencedynamics: {
    transferencePatterns: string[];
    countertransferenceIndicators: string[];
    relationshipProjections: string[];
    therapeuticAllianceQuality: 'poor' | 'developing' | 'good' | 'strong';
  };
  relationalThemes: {
    attachmentStyle: 'secure' | 'anxious' | 'avoidant' | 'disorganized';
    interpersonalPatterns: string[];
    earlyRelationalExperiences: string[];
    currentRelationalDynamics: string[];
  };
}

// Union type for mode-specific analysis
export type ModeSpecificAnalysis = CBTClientStateAnalysis | DBTClientStateAnalysis | PsychodynamicClientStateAnalysis;

// Enhanced patient analysis that includes mode-specific analysis
export interface EnhancedPatientAnalysis extends PatientAnalysis {
  modeSpecificAnalysis: ModeSpecificAnalysis;
  therapistMode: 'CBT' | 'DBT' | 'psychodynamic';
}

export interface TherapistAgent extends AgentConfig {
  type: 'therapist';
  analysisPrompts: {
    sentiment: string;
    motivation: string;
    engagement: string;
  };
}

// Multi-Therapist Response Types
export interface MultiTherapistResponse {
  cbtOnly: TherapistPersonaResponse;
  miFixedPretreatment: TherapistPersonaResponse;
  dynamicAdaptive: TherapistPersonaResponse;
  patientMessage: string;
  timestamp: string;
}

export interface TherapistPersonaResponse extends TherapistResponse {
  personaType: TherapistPersonaType;
  personaName: string;
  strategyState?: {
    currentApproach: 'CBT' | 'MI';
    hasSwitch: boolean;
    switchReason?: string;
    sessionPhase: 'opening' | 'middle' | 'closing';
  };
}

export interface PatientAgent extends AgentConfig {
  type: 'patient';
  emotionalState: EmotionalState;
  backstory?: string;
  currentConcerns?: string[];
}

export interface EmotionalState {
  mood: 'depressed' | 'anxious' | 'neutral' | 'hopeful' | 'frustrated';
  energy: 'low' | 'medium' | 'high';
  openness: 'closed' | 'guarded' | 'open' | 'very_open';
  trust: number; // 0-100
}

export interface ConversationContext {
  id: string;
  messages: Array<{
    id: string;
    conversationId: string;
    sender: 'therapist' | 'patient';
    content: string;
    thinking: string;
    metadata: {
      confidence: number;
      processingTime: number;
      patientAnalysis?: PatientAnalysis;
      therapeuticApproach?: TherapeuticApproachInfo;
    };
    timestamp: string;
  }>;
  currentTurn: number;
  maxTurns: number;
  status: 'active' | 'completed' | 'paused';
}

// Multi-Therapist Conversation Context
export interface MultiTherapistConversationContext {
  id: string;
  patientMessages: Array<{
    id: string;
    conversationId: string;
    content: string;
    timestamp: string;
  }>;
  therapistConversations: {
    cbtOnly: ConversationContext;
    miFixedPretreatment: ConversationContext;
    dynamicAdaptive: ConversationContext;
  };
  currentTurn: number;
  maxTurns: number;
  status: 'active' | 'completed' | 'paused';
  studyMetadata: {
    startTime: string;
    patientPersonaId: string;
    studyConfiguration: {
      readinessThresholds: {
        cbtMinimum: number;
        miMaximum: number;
      };
      allowDynamicSwitching: boolean;
    };
  };
}

export interface AgentResponse {
  message: string;
  thinking: string;
  metadata: {
    confidence: number;
    processingTime: number;
  };
}

export interface TherapistResponse extends AgentResponse {
  metadata: AgentResponse['metadata'] & {
    patientAnalysis?: PatientAnalysis;
    therapeuticApproach?: TherapeuticApproachInfo;
  };
}

export interface PatientResponse extends AgentResponse {
  // Patient responses don't include self-analysis
}

export interface OpenAIRequest {
  messages: Array<{
    role: 'system' | 'user' | 'assistant';
    content: string;
  }>;
  temperature: number;
  max_completion_tokens: number;
}

// Enhanced Database Interfaces for Conversation Storage System

export interface DatabaseConversation {
  id: string;
  status: string;
  config: any;
  session_metadata?: SessionMetadata;
  patient_persona_id?: string;
  therapist_mode?: 'single' | 'multi-therapist';
  session_type?: string;
  created_at: string;
  updated_at: string;
}

export interface DatabaseMessage {
  id: string;
  conversation_id: string;
  sender: string;
  content: string;
  metadata: any;
  message_type?: 'standard' | 'greeting' | 'closing' | 'intervention';
  thinking?: string;
  processing_time?: number;
  confidence_score?: number;
  created_at: string;
}

export interface DatabaseThought {
  id: string;
  conversation_id: string;
  agent_type: string;
  content: string;
  message_id?: string;
  type: string;
  created_at: string;
}

// New Database Interfaces for Enhanced Storage

export interface DatabaseAIAnalysis {
  id: string;
  message_id: string;
  conversation_id: string;
  analysis_type: 'patient_analysis' | 'therapeutic_approach' | 'mode_specific';
  analysis_data: PatientAnalysis | TherapeuticApproachInfo | ModeSpecificAnalysis;
  created_at: string;
}

export interface DatabaseTherapeuticApproach {
  id: string;
  message_id: string;
  conversation_id: string;
  approach_id: string;
  approach_name: string;
  technique_id?: string;
  technique_name?: string;
  rationale?: string;
  effectiveness_prediction?: number;
  created_at: string;
}

export interface DatabaseSessionAnalytics {
  id: string;
  conversation_id: string;
  total_messages: number;
  patient_messages: number;
  therapist_messages: number;
  session_duration?: number;
  avg_response_time?: number;
  sentiment_progression?: Array<{
    timestamp: string;
    sentiment: 'positive' | 'negative' | 'neutral';
    intensity: 'low' | 'medium' | 'high';
  }>;
  engagement_progression?: Array<{
    timestamp: string;
    level: 'low' | 'medium' | 'high';
    patterns: string[];
  }>;
  motivation_progression?: Array<{
    timestamp: string;
    level: 'low' | 'medium' | 'high';
    type: 'intrinsic' | 'extrinsic' | 'mixed';
  }>;
  therapeutic_techniques_used?: string[];
  readiness_score_progression?: Array<{
    timestamp: string;
    score: number;
    factors: ReadinessScore;
  }>;
  created_at: string;
  updated_at: string;
}

export interface DatabaseMultiTherapistSession {
  id: string;
  conversation_id: string;
  persona_type: TherapistPersonaType;
  persona_name: string;
  strategy_state?: any;
  performance_metrics?: {
    avg_response_time: number;
    technique_effectiveness: { [techniqueId: string]: number };
    patient_engagement_scores: number[];
    readiness_improvement: number;
  };
  created_at: string;
  updated_at: string;
}

// Session Metadata Interface
export interface SessionMetadata {
  startTime: string;
  endTime?: string;
  duration?: number;
  patientPersonaId?: string | undefined;
  therapistConfiguration?: any;
  studyConfiguration?: any;
  environmentInfo?: {
    platform: string;
    userAgent?: string;
    sessionId: string;
  };
}

export interface WebSocketClient {
  id: string;
  socket: any;
  conversationId?: string;
  lastActivity: string;
}

export interface ConversationOrchestrator {
  conversationId: string;
  therapist: TherapistAgent;
  patient: PatientAgent;
  context: ConversationContext;
  clients: WebSocketClient[];
}

// CBT Evaluation Types
export interface CBTEvaluationDimension {
  name: string;
  score: number; // 0, 2, 4, or 6
  criteria: string;
  rationale?: string;
}

export interface CBTEvaluationResult {
  id: string;
  conversationId: string;
  evaluationTimestamp: string;
  dimensions: {
    cbtValidity: CBTEvaluationDimension;
    cbtAppropriateness: CBTEvaluationDimension;
    cbtAccuracy: CBTEvaluationDimension;
    esAppropriateness: CBTEvaluationDimension;
    stability: CBTEvaluationDimension;
  };
  overallScore: number; // Average of all dimension scores
  overallAssessment: 'poor' | 'fair' | 'good' | 'excellent';
  conversationSummary: string;
  recommendations: string[];
  metadata: {
    totalMessages: number;
    sessionDuration: number; // in minutes
    evaluationModel: string;
    evaluationVersion: string;
  };
}

// Comparative Evaluation Types
export interface ComparativeCBTEvaluationResult {
  id: string;
  conversationId: string;
  evaluationTimestamp: string;
  evaluations: {
    cbtOnly: CBTEvaluationResult;
    miFixedPretreatment: CBTEvaluationResult;
    dynamicAdaptive: CBTEvaluationResult;
  };
  comparison: {
    bestPerforming: TherapistPersonaType;
    scoreComparison: {
      cbtOnly: number;
      miFixedPretreatment: number;
      dynamicAdaptive: number;
    };
    dimensionComparison: {
      [dimension: string]: {
        cbtOnly: number;
        miFixedPretreatment: number;
        dynamicAdaptive: number;
        winner: TherapistPersonaType;
      };
    };
    insights: string[];
    recommendations: string[];
  };
  studyMetadata: {
    patientPersonaId: string;
    sessionDuration: number;
    totalPatientMessages: number;
    readinessScoreProgression: {
      initial: number;
      final: number;
      average: number;
    };
  };
}

export interface CBTEvaluationRequest {
  conversationId: string;
  messages: Array<{
    sender: 'therapist' | 'patient';
    content: string;
    timestamp: string;
  }>;
  sessionMetadata?: {
    duration?: number;
    patientPersona?: string;
    therapeuticApproaches?: string[];
  };
}

export interface DatabaseEvaluation {
  id: string;
  conversation_id: string;
  evaluation_data: CBTEvaluationResult;
  created_at: string;
  updated_at: string;
}

// Database Service Operation Interfaces

export interface ConversationStorageData {
  id?: string;
  status: 'active' | 'completed' | 'paused';
  config: any;
  sessionMetadata?: SessionMetadata;
  patientPersonaId?: string;
  therapistMode?: 'single' | 'multi-therapist';
  sessionType?: string;
}

export interface MessageStorageData {
  id?: string;
  conversationId: string;
  sender: 'therapist' | 'patient';
  content: string;
  metadata?: any;
  messageType?: 'standard' | 'greeting' | 'closing' | 'intervention';
  thinking?: string;
  processingTime?: number;
  confidenceScore?: number;
}

export interface AIAnalysisStorageData {
  messageId: string;
  conversationId: string;
  analysisType: 'patient_analysis' | 'therapeutic_approach' | 'mode_specific';
  analysisData: PatientAnalysis | TherapeuticApproachInfo | ModeSpecificAnalysis;
}

export interface TherapeuticApproachStorageData {
  messageId: string;
  conversationId: string;
  approachId: string;
  approachName: string;
  techniqueId?: string;
  techniqueName?: string;
  rationale?: string;
  effectivenessPrediction?: number;
}

export interface SessionAnalyticsStorageData {
  conversationId: string;
  totalMessages: number;
  patientMessages: number;
  therapistMessages: number;
  sessionDuration?: number;
  avgResponseTime?: number;
  sentimentProgression?: Array<{
    timestamp: string;
    sentiment: 'positive' | 'negative' | 'neutral';
    intensity: 'low' | 'medium' | 'high';
  }>;
  engagementProgression?: Array<{
    timestamp: string;
    level: 'low' | 'medium' | 'high';
    patterns: string[];
  }>;
  motivationProgression?: Array<{
    timestamp: string;
    level: 'low' | 'medium' | 'high';
    type: 'intrinsic' | 'extrinsic' | 'mixed';
  }>;
  therapeuticTechniquesUsed?: string[];
  readinessScoreProgression?: Array<{
    timestamp: string;
    score: number;
    factors: ReadinessScore;
  }>;
}

export interface MultiTherapistSessionStorageData {
  conversationId: string;
  personaType: TherapistPersonaType;
  personaName: string;
  strategyState?: any;
  performanceMetrics?: {
    avgResponseTime: number;
    techniqueEffectiveness: { [techniqueId: string]: number };
    patientEngagementScores: number[];
    readinessImprovement: number;
  };
}

// Database Query Interfaces

export interface ConversationQueryOptions {
  status?: 'active' | 'completed' | 'paused';
  patientPersonaId?: string;
  therapistMode?: 'single' | 'multi-therapist';
  sessionType?: string;
  dateRange?: {
    start: string;
    end: string;
  };
  limit?: number;
  offset?: number;
  orderBy?: 'created_at' | 'updated_at';
  orderDirection?: 'asc' | 'desc';
}

export interface MessageQueryOptions {
  conversationId?: string;
  sender?: 'therapist' | 'patient';
  messageType?: 'standard' | 'greeting' | 'closing' | 'intervention';
  dateRange?: {
    start: string;
    end: string;
  };
  includeAnalysis?: boolean;
  includeTherapeuticApproaches?: boolean;
  limit?: number;
  offset?: number;
}

export interface AnalysisQueryOptions {
  conversationId?: string;
  messageId?: string;
  analysisType?: 'patient_analysis' | 'therapeutic_approach' | 'mode_specific';
  dateRange?: {
    start: string;
    end: string;
  };
  limit?: number;
  offset?: number;
}
