// Multi-Therapist Agent Service for Comparative Study
import { OpenAIService } from '../openai.js';
import { 
  MultiTherapistResponse, 
  TherapistPersonaResponse, 
  PatientAnalysis, 
  ConversationContext,
  TherapistPersonaType,
  MultiTherapistConversationContext
} from '../../types/index.js';
import { TherapistConfig, defaultConversationConfig } from '../../config/conversation.js';
import {
  BaseTherapistPersonaStrategy,
  createTherapistPersonaStrategy,
  PersonaStrategyContext
} from '../therapist-persona-strategies.js';
import { TemplateRenderer } from '../template-renderer.js';
import { TechniqueSelector } from '../technique-selector.js';

export class MultiTherapistAgentService {
  private openaiService: OpenAIService;
  private config: TherapistConfig;
  private templateRenderer: TemplateRenderer;
  private techniqueSelector: TechniqueSelector;
  
  // Three therapist persona strategies
  private strategies: {
    cbtOnly: BaseTherapistPersonaStrategy;
    miFixedPretreatment: BaseTherapistPersonaStrategy;
    dynamicAdaptive: BaseTherapistPersonaStrategy;
  };

  // Track previous techniques for each persona
  private previousTechniques: {
    cbtOnly: string[];
    miFixedPretreatment: string[];
    dynamicAdaptive: string[];
  };

  constructor(openaiService: OpenAIService, config?: Partial<TherapistConfig>) {
    this.openaiService = openaiService;
    this.config = { ...defaultConversationConfig.therapist, ...config };
    this.templateRenderer = new TemplateRenderer();
    this.techniqueSelector = new TechniqueSelector();
    
    // Initialize the three therapist persona strategies
    this.strategies = {
      cbtOnly: createTherapistPersonaStrategy('cbt-only'),
      miFixedPretreatment: createTherapistPersonaStrategy('mi-fixed-pretreatment', {
        cbtThreshold: this.config.therapeuticApproaches.readinessThresholds.cbtMinimum
      }),
      dynamicAdaptive: createTherapistPersonaStrategy('dynamic-adaptive', {
        cbtThreshold: this.config.therapeuticApproaches.readinessThresholds.cbtMinimum,
        miThreshold: this.config.therapeuticApproaches.readinessThresholds.miMaximum
      })
    };

    // Initialize technique tracking
    this.previousTechniques = {
      cbtOnly: [],
      miFixedPretreatment: [],
      dynamicAdaptive: []
    };
  }

  /**
   * Generate responses from all three therapist personas for a patient message
   */
  async generateMultiTherapistResponse(
    patientMessage: string,
    conversationContext: MultiTherapistConversationContext
  ): Promise<MultiTherapistResponse> {
    console.log('🧠 Generating multi-therapist response...');

    try {
      // Perform comprehensive patient analysis (shared across all personas)
      const patientAnalysis = await this.openaiService.analyzePatient(patientMessage);

      // Create strategy context for all personas
      const strategyContext: PersonaStrategyContext = {
        messageCount: conversationContext.currentTurn,
        previousTechniques: [], // Will be set per persona
        sessionPhase: this.getSessionPhase(conversationContext.currentTurn),
        conversationHistory: this.buildConversationHistory(conversationContext)
      };

      // Generate responses from all three personas in parallel
      const [cbtOnlyResponse, miFixedResponse, dynamicResponse] = await Promise.all([
        this.generatePersonaResponse(
          'cbtOnly',
          patientMessage,
          patientAnalysis,
          { ...strategyContext, previousTechniques: this.previousTechniques.cbtOnly },
          conversationContext.therapistConversations.cbtOnly
        ),
        this.generatePersonaResponse(
          'miFixedPretreatment',
          patientMessage,
          patientAnalysis,
          { ...strategyContext, previousTechniques: this.previousTechniques.miFixedPretreatment },
          conversationContext.therapistConversations.miFixedPretreatment
        ),
        this.generatePersonaResponse(
          'dynamicAdaptive',
          patientMessage,
          patientAnalysis,
          { ...strategyContext, previousTechniques: this.previousTechniques.dynamicAdaptive },
          conversationContext.therapistConversations.dynamicAdaptive
        )
      ]);

      return {
        cbtOnly: cbtOnlyResponse,
        miFixedPretreatment: miFixedResponse,
        dynamicAdaptive: dynamicResponse,
        patientMessage,
        timestamp: new Date().toISOString()
      };

    } catch (error) {
      console.error('❌ Error generating multi-therapist response:', error);
      throw error;
    }
  }

  /**
   * Generate response from a specific therapist persona
   */
  private async generatePersonaResponse(
    personaKey: 'cbtOnly' | 'miFixedPretreatment' | 'dynamicAdaptive',
    patientMessage: string,
    patientAnalysis: PatientAnalysis,
    strategyContext: PersonaStrategyContext,
    conversationContext: ConversationContext
  ): Promise<TherapistPersonaResponse> {
    const strategy = this.strategies[personaKey];
    
    // Select therapeutic approach and technique based on persona strategy
    const therapeuticApproachWithState = strategy.selectApproachAndTechnique(
      patientAnalysis,
      strategyContext
    );

    // Update technique tracking
    this.previousTechniques[personaKey].push(therapeuticApproachWithState.selectedTechnique.id);

    // Get technique-specific prompt
    const techniquePrompt = this.getTechniquePrompt(therapeuticApproachWithState.selectedTechnique.id);

    // Build system prompt with therapeutic approach
    const systemPrompt = this.buildSystemPromptWithApproach(
      patientAnalysis,
      therapeuticApproachWithState,
      techniquePrompt,
      strategy.name
    );

    // Build conversation messages for this persona
    const conversationMessages = this.buildConversationMessages(patientMessage, conversationContext);

    // Generate the main response using therapeutic approach
    const response = await this.openaiService.generateResponse({
      messages: [
        { role: 'system', content: systemPrompt },
        ...conversationMessages
      ],
      temperature: 0.7,
      max_completion_tokens: 1000
    });

    // Generate thinking process
    const thinking = await this.openaiService.generateThinking(
      'therapist',
      `Patient said: "${patientMessage}". Using ${therapeuticApproachWithState.name} with ${therapeuticApproachWithState.selectedTechnique.name} technique.`,
      `Analyzing patient readiness (${patientAnalysis.readinessScore.score}/10) and selecting appropriate therapeutic response as ${strategy.name}.`
    );

    return {
      message: response.message,
      thinking: thinking,
      metadata: {
        ...response.metadata,
        patientAnalysis,
        therapeuticApproach: {
          id: therapeuticApproachWithState.id,
          name: therapeuticApproachWithState.name,
          selectedTechnique: therapeuticApproachWithState.selectedTechnique
        }
      },
      personaType: strategy.personaType,
      personaName: strategy.name,
      strategyState: therapeuticApproachWithState.strategyState
    };
  }

  /**
   * Generate initial greetings from all three personas
   */
  async generateInitialGreetings(): Promise<MultiTherapistResponse> {
    console.log('👋 Generating initial greetings from all personas...');

    const greetingPrompt = this.templateRenderer.render(this.config.prompts.initialGreeting, {
      therapistName: 'Dr. Montri',
      sessionType: 'therapy session'
    });

    // Generate greetings from all three personas in parallel
    const [cbtOnlyGreeting, miFixedGreeting, dynamicGreeting] = await Promise.all([
      this.generatePersonaGreeting('cbtOnly', greetingPrompt),
      this.generatePersonaGreeting('miFixedPretreatment', greetingPrompt),
      this.generatePersonaGreeting('dynamicAdaptive', greetingPrompt)
    ]);

    return {
      cbtOnly: cbtOnlyGreeting,
      miFixedPretreatment: miFixedGreeting,
      dynamicAdaptive: dynamicGreeting,
      patientMessage: '',
      timestamp: new Date().toISOString()
    };
  }

  private async generatePersonaGreeting(
    personaKey: 'cbtOnly' | 'miFixedPretreatment' | 'dynamicAdaptive',
    greetingPrompt: string
  ): Promise<TherapistPersonaResponse> {
    const strategy = this.strategies[personaKey];
    
    const systemPrompt = `You are ${strategy.name}. ${strategy.description}. 
    
    ${greetingPrompt}
    
    Keep your greeting warm, professional, and consistent with your therapeutic approach. Limit to 2-3 sentences.`;

    const response = await this.openaiService.generateResponse({
      messages: [
        { role: 'system', content: systemPrompt },
        { role: 'user', content: 'Please provide your initial greeting to start the therapy session.' }
      ],
      temperature: 0.7,
      max_completion_tokens: 1000
    });

    const thinking = await this.openaiService.generateThinking(
      'therapist',
      'Starting therapy session',
      `Providing initial greeting as ${strategy.name} with focus on ${strategy.description.toLowerCase()}.`
    );

    return {
      message: response.message,
      thinking: thinking,
      metadata: response.metadata,
      personaType: strategy.personaType,
      personaName: strategy.name,
      strategyState: strategy.getStrategyState()
    };
  }

  private buildSystemPromptWithApproach(
    patientAnalysis: PatientAnalysis,
    therapeuticApproach: any,
    techniquePrompt: string,
    personaName: string
  ): string {
    return this.templateRenderer.render(this.config.prompts.systemPrompt, {
      therapistName: personaName,
      patientAnalysis: JSON.stringify(patientAnalysis, null, 2),
      therapeuticApproach: therapeuticApproach.name,
      selectedTechnique: therapeuticApproach.selectedTechnique.name,
      techniqueDescription: therapeuticApproach.selectedTechnique.description,
      techniquePrompt,
      readinessScore: patientAnalysis.readinessScore.score,
      recommendedApproach: patientAnalysis.readinessScore.recommendedApproach
    });
  }

  private buildConversationMessages(patientMessage: string, conversationContext: ConversationContext) {
    const messages = conversationContext.messages.map(msg => ({
      role: msg.sender === 'therapist' ? 'assistant' : 'user' as 'assistant' | 'user',
      content: msg.content
    }));

    messages.push({
      role: 'user',
      content: patientMessage
    });

    return messages;
  }

  private buildConversationHistory(conversationContext: MultiTherapistConversationContext) {
    return conversationContext.patientMessages.map(msg => ({
      sender: 'patient' as 'therapist' | 'patient',
      content: msg.content,
      timestamp: msg.timestamp
    }));
  }

  private getSessionPhase(messageCount: number): 'opening' | 'middle' | 'closing' {
    if (messageCount <= 6) return 'opening';
    if (messageCount <= 16) return 'middle';
    return 'closing';
  }

  private getTechniquePrompt(techniqueId: string): string {
    return this.techniqueSelector.getTechniquePrompt(techniqueId);
  }

  /**
   * Reset all persona strategies (useful for new conversations)
   */
  resetStrategies(): void {
    Object.values(this.strategies).forEach(strategy => strategy.reset());
    this.previousTechniques = {
      cbtOnly: [],
      miFixedPretreatment: [],
      dynamicAdaptive: []
    };
  }
}
